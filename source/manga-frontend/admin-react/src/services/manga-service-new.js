import { toast } from "react-toastify";
import { mangaHttpClient } from "./http-client";
import { logApiCall } from "../utils/api-logger";

class MangaService {
    async getMyMangas(page = 0, size = 10, keyword) {
        logApiCall('getMyMangas');
        try {
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('size', size.toString());
            if (keyword && keyword.trim()) {
                params.append('keyword', keyword.trim());
            }

            const apiResponse = await mangaHttpClient.get(`/mangas/my-mangas?${params.toString()}`);

            if (apiResponse.code !== 200) {
                toast.error(apiResponse.message || "Không thể lấy danh sách truyện của bạn", { position: "top-right" });
                return null;
            }

            apiResponse.result.content.forEach(manga => {
                if (!manga.coverUrl) {
                    manga.coverUrl = '/images/default-manga-cover.jpg';
                }
            });

            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi lấy danh sách truyện của tôi:", error);
            return null;
        }
    }

    async getAllMangas(page = 0, size = 10, keyword, genreName, status, yearOfRelease) {
        logApiCall('getAllMangas');
        try {
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('size', size.toString());
            if (keyword && keyword.trim()) {
                params.append('keyword', keyword.trim());
            }
            if (genreName && genreName.trim()) {
                params.append('genreName', genreName.trim());
            }
            if (status && status.trim()) {
                params.append('status', status.trim());
            }
            if (yearOfRelease) {
                params.append('yearOfRelease', yearOfRelease.toString());
            }
            const apiResponse = await mangaHttpClient.get(`/mangas?${params.toString()}`);

            if (apiResponse.code !== 200) {
                toast.error(apiResponse.message || "Không thể lấy danh sách manga", { position: "top-right" });
                return null;
            }
            
            apiResponse.result.content.forEach(manga => {
                if (!manga.coverUrl) {
                    manga.coverUrl = '/images/default-manga-cover.jpg';
                }
            });
            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi lấy danh sách manga:", error);
            return null;
        }
    }

    async createManga(formData) {
        logApiCall('createManga');
        try {
            const apiResponse = await mangaHttpClient.post('/mangas', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            if (apiResponse.code !== 201) {
                toast.error(apiResponse.message || "Không thể tạo truyện mới", { position: "top-right" });
                return null;
            }

            toast.success("Tạo truyện mới thành công", { position: "top-right" });
            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi tạo truyện mới:", error);
            toast.error("Đã xảy ra lỗi khi tạo truyện mới", { position: "top-right" });
            return null;
        }
    }

    async updateManga(id, formData) {
        logApiCall('updateManga');
        try {
            const apiResponse = await mangaHttpClient.put(`/mangas/${id}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            if (apiResponse.code !== 200) {
                toast.error(apiResponse.message || "Không thể cập nhật truyện", { position: "top-right" });
                return null;
            }

            toast.success("Cập nhật truyện thành công", { position: "top-right" });
            return apiResponse.result;
        } catch (error) {
            console.error(`Lỗi cập nhật truyện ID ${id}:`, error);
            toast.error("Đã xảy ra lỗi khi cập nhật truyện", { position: "top-right" });
            return null;
        }
    }

    async deleteManga(id) {
        logApiCall('deleteManga');
        try {
            const apiResponse = await mangaHttpClient.delete(`/mangas/${id}`);

            if (apiResponse.code !== 200) {
                toast.error(apiResponse.message || "Không thể xóa truyện", { position: "top-right" });
                return false;
            }

            toast.success("Xóa truyện thành công", { position: "top-right" });
            return true;
        } catch (error) {
            console.error(`Lỗi xóa truyện ID ${id}:`, error);
            toast.error("Đã xảy ra lỗi khi xóa truyện", { position: "top-right" });
            return false;
        }
    }

    async createChapter(formData) {
        logApiCall('createChapter');
        try {
            const apiResponse = await mangaHttpClient.post('/chapters', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            if (apiResponse.code !== 201) {
                toast.error(apiResponse.message || "Không thể tạo chapter mới", { position: "top-right" });
                return null;
            }

            toast.success("Tạo chapter mới thành công", { position: "top-right" });
            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi tạo chapter mới:", error);
            toast.error("Đã xảy ra lỗi khi tạo chapter mới", { position: "top-right" });
            return null;
        }
    }

    async deleteChapter(id) {
        logApiCall('deleteChapter');
        try {
            const apiResponse = await mangaHttpClient.delete(`/chapters/${id}`);

            if (apiResponse.code !== 200) {
                toast.error(apiResponse.message || "Không thể xóa chapter", { position: "top-right" });
                return false;
            }

            toast.success("Xóa chapter thành công", { position: "top-right" });
            return true;
        } catch (error) {
            console.error(`Lỗi xóa chapter ID ${id}:`, error);
            toast.error("Đã xảy ra lỗi khi xóa chapter", { position: "top-right" });
            return false;
        }
    }

    async getAllChapters(page = 0, size = 10, mangaId) {
        logApiCall('getAllChapters');
        try {
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('size', size.toString());

            if (mangaId && mangaId.trim()) {
                params.append('mangaId', mangaId.trim());
            }

            const url = `/chapters?${params.toString()}`;
            const apiResponse = await mangaHttpClient.get(url);

            if (apiResponse.code !== 200) {
                console.error("Không thể lấy danh sách chapter:", apiResponse.message);
                return null;
            }

            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi lấy danh sách chapter:", error);
            return null;
        }
    }

    async getChaptersByMangaId(mangaId) {
        logApiCall('getChaptersByMangaId');
        try {
            const apiResponse = await mangaHttpClient.get(`/chapters/manga/${mangaId}`);

            if (apiResponse.code !== 200) {
                console.error(`Không thể lấy danh sách chapter của truyện ${mangaId}:`, apiResponse.message);
                return [];
            }

            return apiResponse.result;
        } catch (error) {
            console.error(`Lỗi lấy danh sách chapter của truyện ${mangaId}:`, error);
            return [];
        }
    }

    async getHighestChapterNumber(mangaId) {
        logApiCall('getHighestChapterNumber');
        try {
            const apiResponse = await mangaHttpClient.get(`/mangas/${mangaId}/highest-chapter-number`);

            if (apiResponse.code !== 200) {
                console.error(`Không thể lấy số chapter cao nhất của truyện ${mangaId}:`, apiResponse.message);
                return 0;
            }

            return apiResponse.result;
        } catch (error) {
            console.error(`Lỗi lấy số chapter cao nhất của truyện ${mangaId}:`, error);
            return 0;
        }
    }

    async countMangas(includeDeleted = false) {
        logApiCall('countMangas');
        try {
            const apiResponse = await mangaHttpClient.get(`/mangas/count?includeDeleted=${includeDeleted}`);

            if (apiResponse.code !== 200) {
                toast.error(apiResponse.message || "Không thể đếm tổng số truyện", { position: "top-right" });
                return 0;
            }

            return apiResponse.result;
        } catch (error) {
            console.error(`Lỗi đếm tổng số truyện:`, error);
            return 0;
        }
    }

    async getMangaStatistics() {
        logApiCall('getMangaStatistics');
        try {
            const apiResponse = await mangaHttpClient.get('/mangas/statistics');

            if (apiResponse.code !== 200) {
                toast.error(apiResponse.message || "Không thể lấy thống kê truyện", { position: "top-right" });
                return null;
            }

            return apiResponse.result;
        } catch (error) {
            console.error(`Lỗi lấy thống kê truyện:`, error);
            return null;
        }
    }

    async quickSearchManga(keyword, limit = 10) {
        logApiCall('quickSearchManga');
        try {
            const url = `/mangas/search/quick?keyword=${encodeURIComponent(keyword)}&limit=${limit}`;
            const apiResponse = await mangaHttpClient.get(url);

            if (apiResponse.code !== 200) {
                console.error("Không thể tìm kiếm nhanh manga:", apiResponse.message);
                return [];
            }

            apiResponse.result.forEach(manga => {
                if (!manga.coverUrl) {
                    manga.coverUrl = '/images/default-manga-cover.jpg';
                }
            });

            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi tìm kiếm nhanh manga:", error);
            return [];
        }
    }
}

export default new MangaService();
