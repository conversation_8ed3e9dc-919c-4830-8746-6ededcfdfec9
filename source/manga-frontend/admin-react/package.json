{"name": "admin-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@material/web": "^2.2.0", "@radix-ui/react-slot": "^1.1.2", "@react-oauth/google": "^0.12.1", "@tailwindcss/vite": "^4.0.12", "@tanstack/react-query": "^5.77.0", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^11.7.1", "lucide-react": "^0.482.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.3.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "swiper": "^11.2.5", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.12", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}